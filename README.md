# 彩票号码过滤器 Android应用 / Lottery Filter Android App

一个功能强大的Android彩票号码过滤应用，采用完全中文界面和可视化数字选择器。应用生成所有可能的5数字组合（从1-35中选择，共324,632种组合）并提供高级过滤功能。

A powerful Android application for generating and filtering lottery number combinations with full Chinese interface and visual number selectors. This app generates all possible 5-number combinations from a range of 1-35 (324,632 total combinations) and provides advanced filtering capabilities.

## 功能特色 / Features

### 核心功能 / Core Functionality
- **组合生成 / Combination Generation**: 生成所有可能的5数字组合（1-35）/ Generates all possible 5-number combinations from 1-35
- **双重过滤系统 / Dual Filtering System**:
  - **排除组合数字 / Exclude Combination Numbers**: 排除可以从指定数字集合中形成的所有组合 / Remove all combinations that can be formed from a specified set of numbers
  - **排除单个数字 / Exclude Individual Numbers**: 排除包含特定数字的所有组合 / Remove all combinations containing specific numbers
- **实时处理 / Real-time Processing**: 后台处理，带有进度指示器 / Background processing with progress indicators
- **高效算法 / Efficient Algorithms**: 优化处理大数据集（30万+组合）/ Optimized for handling large datasets (300k+ combinations)

### 用户界面 / User Interface
- **完全中文界面 / Full Chinese Interface**: 所有文本、标签、按钮均为中文 / All text, labels, and buttons in Chinese
- **可视化数字选择器 / Visual Number Selectors**: 点击式数字选择，替代文本输入 / Click-based number selection replacing text input
- **图标增强界面 / Icon-Enhanced Interface**: 彩票主题图标和Material Design图标 / Lottery-themed and Material Design icons
- **颜色编码显示 / Color-coded Display**: 不同数字范围使用不同颜色 / Different colors for different number ranges
- **响应式设计 / Responsive Design**: 基于卡片的布局设计 / Card-based layout design

### Performance Features
- **Memory Optimization**: Processes combinations in batches to avoid memory issues
- **Background Processing**: Uses Kotlin coroutines for non-blocking operations
- **Caching System**: Caches results for quick retrieval
- **Early Filtering**: Applies filters during generation to improve performance

## Technical Specifications

### Architecture
- **MVVM Pattern**: Clean separation of concerns
- **Kotlin Coroutines**: Asynchronous processing
- **Flow**: Reactive data streams
- **ViewBinding**: Type-safe view references
- **RecyclerView**: Efficient list display with DiffUtil

### Dependencies
- AndroidX Core KTX
- Material Design Components
- Lifecycle ViewModel & LiveData
- Kotlin Coroutines
- RecyclerView & CardView
- JUnit & Espresso for testing

## Usage Examples

### Example 1: Exclude Combination Numbers
**Input**: Exclude combination numbers: `1,2,3,4,5,6,7,8,9`
**Result**: Removes all 5-number combinations that can be formed from these 9 numbers (126 combinations removed)

### Example 2: Exclude Individual Numbers
**Input**: Exclude individual numbers: `15,25,35`
**Result**: Removes all combinations containing any of these numbers

### Example 3: Combined Filtering
**Input**: 
- Exclude combination numbers: `1,2,3,4,5,6,7,8,9`
- Exclude individual numbers: `15`
**Result**: Applies both filters simultaneously

## Installation & Setup

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK API 24+ (Android 7.0)
- Kotlin 1.9.0+

### Build Instructions
1. Clone the repository
2. Open in Android Studio
3. Sync Gradle files
4. Build and run on device/emulator

```bash
git clone <repository-url>
cd yuce
./gradlew assembleDebug
```

### Running Tests
```bash
# Unit tests
./gradlew test

# Instrumented tests
./gradlew connectedAndroidTest
```

## Project Structure

```
app/
├── src/main/java/com/lotteryfilter/app/
│   ├── engine/              # Core algorithms
│   │   ├── CombinationGenerator.kt
│   │   └── FilterEngine.kt
│   ├── model/               # Data models
│   │   ├── LotteryCombination.kt
│   │   └── FilterCriteria.kt
│   ├── ui/                  # UI components
│   │   └── CombinationAdapter.kt
│   ├── utils/               # Utilities
│   │   └── PerformanceOptimizer.kt
│   ├── viewmodel/           # ViewModels
│   │   └── MainViewModel.kt
│   └── MainActivity.kt
├── src/test/                # Unit tests
└── src/androidTest/         # Instrumented tests
```

## Algorithm Details

### Combination Generation
- Uses iterative algorithm to avoid recursion stack overflow
- Generates combinations in lexicographic order
- Memory-efficient batch processing
- Total combinations: C(35,5) = 324,632

### Filtering Logic
1. **Individual Number Exclusion**: O(1) lookup using HashSet
2. **Combination Number Exclusion**: Checks if all numbers in combination exist in excluded set
3. **Early Filtering**: Applies filters during generation to reduce memory usage

### Performance Optimizations
- Batch processing (1000-5000 combinations per batch)
- Coroutine-based background processing
- Result caching with LRU eviction
- Bit manipulation for fast number checking

## Testing

### Unit Tests
- Model validation tests
- Algorithm correctness tests
- Filter logic tests
- Edge case handling

### UI Tests
- User interaction tests
- Input validation tests
- Progress indicator tests

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions, please create an issue in the repository.
