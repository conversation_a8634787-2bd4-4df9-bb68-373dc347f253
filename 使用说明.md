# 彩票号码过滤器 - 使用说明

## 应用简介

这是一个功能强大的Android彩票号码过滤应用，专门用于生成和过滤5个数字的彩票组合（从1-35中选择）。应用采用完全中文界面，提供直观的数字选择器和高效的过滤算法。

## 主要功能

### 🎯 核心功能
- **组合生成**: 生成所有可能的5数字组合（共324,632种组合）
- **双重过滤系统**:
  - **排除组合数字**: 排除可以从指定数字集合中形成的所有组合
  - **排除单个数字**: 排除包含特定数字的所有组合
- **实时处理**: 后台处理，带有进度指示器
- **高效算法**: 优化处理大数据集（30万+组合）

### 🎨 界面特色
- **完全中文界面**: 所有文本、标签、按钮均为中文
- **可视化数字选择器**: 点击式数字选择，替代传统文本输入
- **图标增强**: 彩票主题图标和Material Design图标
- **颜色编码**: 不同数字范围使用不同颜色显示
- **响应式设计**: 适配不同屏幕尺寸

## 使用方法

### 1. 排除组合数字
1. 在"排除组合数字"区域点击要排除的数字
2. 选中的数字会高亮显示
3. 系统会排除所有可以从这些数字中形成的5数字组合

**示例**: 选择数字 1,2,3,4,5,6,7,8,9
- 结果: 排除所有可以从这9个数字中选择的5数字组合（共126种组合）

### 2. 排除单个数字
1. 在"排除单个数字"区域点击要排除的数字
2. 系统会排除所有包含这些数字的组合

**示例**: 选择数字 15,25,35
- 结果: 排除所有包含15、25或35的组合

### 3. 组合过滤
1. 可以同时使用两种过滤方式
2. 点击"生成并过滤"按钮开始处理
3. 查看过滤结果和统计信息

## 界面说明

### 主界面元素

#### 🎲 应用标题区域
- 彩票球图标 + "彩票号码过滤器"标题
- 功能描述文字

#### 📊 排除组合数字区域
- 数字图标 + 区域标题
- 7×5网格数字选择器（1-35）
- 已选择数字显示区域
- 清除按钮

#### 🔍 排除单个数字区域
- 过滤器图标 + 区域标题
- 7×5网格数字选择器（1-35）
- 已选择数字显示区域
- 清除按钮

#### ⚡ 操作按钮
- **生成并过滤**: 开始处理（带过滤器图标）
- **全部清除**: 清除所有选择（带清除图标）

#### 📈 结果显示区域
- 彩票球图标 + "过滤结果"标题
- 统计信息显示
- 组合列表（卡片式显示）

### 数字选择器特性

#### 🎯 交互反馈
- **未选择**: 灰色背景，黑色文字
- **已选择**: 紫色背景，白色文字，阴影效果
- **点击反馈**: 即时视觉变化和Toast提示

#### 🌈 颜色编码（结果显示）
- **1-7**: 蓝色
- **8-14**: 绿色
- **15-21**: 橙色
- **22-28**: 紫色
- **29-35**: 红色

## 技术特性

### 🚀 性能优化
- **批处理**: 分批处理组合（1000-5000个/批）
- **后台处理**: 使用Kotlin协程，不阻塞UI
- **内存管理**: 高效内存使用，避免内存溢出
- **结果缓存**: 缓存过滤结果，快速检索
- **早期过滤**: 生成过程中应用过滤器

### 🏗️ 架构设计
- **MVVM模式**: 清晰的关注点分离
- **响应式编程**: Flow数据流
- **类型安全**: ViewBinding视图绑定
- **高效列表**: RecyclerView + DiffUtil

## 使用示例

### 示例1: 排除特定组合
**场景**: 不想要从前9个数字中选择的任何组合
**操作**: 
1. 在"排除组合数字"中选择: 1,2,3,4,5,6,7,8,9
2. 点击"生成并过滤"
**结果**: 排除126种组合，剩余324,506种组合

### 示例2: 排除不吉利数字
**场景**: 不想要包含数字4和13的组合
**操作**:
1. 在"排除单个数字"中选择: 4,13
2. 点击"生成并过滤"
**结果**: 排除所有包含4或13的组合

### 示例3: 组合过滤
**场景**: 既不要前9个数字的组合，也不要包含15的组合
**操作**:
1. 排除组合数字: 1,2,3,4,5,6,7,8,9
2. 排除单个数字: 15
3. 点击"生成并过滤"
**结果**: 应用两种过滤器，获得最终结果

## 注意事项

### ⚠️ 使用建议
- 建议在WiFi环境下使用，处理大量数据
- 首次运行可能需要较长时间（几分钟）
- 可以随时取消正在进行的操作
- 建议选择合理的过滤条件，避免结果过少

### 📱 系统要求
- Android 7.0 (API 24) 或更高版本
- 至少2GB RAM推荐
- 100MB可用存储空间

### 🔧 故障排除
- 如果应用响应缓慢，请等待处理完成
- 如果内存不足，尝试重启应用
- 清除过多的选择可以提高性能

## 技术支持

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送反馈邮件

---

**版本**: 1.0.0  
**更新日期**: 2024年  
**开发语言**: Kotlin  
**UI框架**: Material Design 3
