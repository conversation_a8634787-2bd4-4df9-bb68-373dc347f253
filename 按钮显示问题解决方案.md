# 按钮显示问题解决方案

## 🔍 问题分析

您遇到的按钮显示问题（按钮是白色的，不显示数字）通常是由以下原因造成的：

### 可能的原因：
1. **MaterialButton样式冲突** - Material Design组件可能有默认样式覆盖
2. **背景色设置问题** - backgroundTint可能没有正确应用
3. **文字颜色问题** - 文字颜色可能与背景色相同
4. **资源引用问题** - 颜色资源可能没有正确加载

## ✅ 解决方案

我已经创建了一个简化版的数字选择器 `SimpleNumberSelectorView`，它使用以下方法确保按钮正确显示：

### 1. 使用普通Button而不是MaterialButton
```kotlin
// 避免Material Design样式冲突
val button = Button(context).apply {
    text = number.toString()
    // ...
}
```

### 2. 程序化创建背景
```kotlin
private fun createButtonBackground(isSelected: Boolean): GradientDrawable {
    return GradientDrawable().apply {
        shape = GradientDrawable.RECTANGLE
        cornerRadius = 20f
        
        if (isSelected) {
            setColor(ContextCompat.getColor(context, R.color.purple_500))
            setStroke(6, ContextCompat.getColor(context, R.color.purple_700))
        } else {
            setColor(ContextCompat.getColor(context, R.color.light_gray))
            setStroke(3, ContextCompat.getColor(context, R.color.combination_border))
        }
    }
}
```

### 3. 明确设置文字颜色
```kotlin
// 确保文字颜色正确显示
if (isSelected) {
    button.setTextColor(Color.WHITE)
} else {
    button.setTextColor(Color.BLACK)
}
```

### 4. 固定按钮尺寸
```kotlin
// 使用固定尺寸避免资源问题
val buttonSize = 120 // 固定大小
layoutParams = GridLayout.LayoutParams().apply {
    width = buttonSize
    height = buttonSize
    // ...
}
```

## 🔧 实施步骤

### 步骤1: 更新布局文件
布局文件已更新为使用 `SimpleNumberSelectorView`：

```xml
<com.lotteryfilter.app.ui.SimpleNumberSelectorView
    android:id="@+id/numberSelectorCombinations"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" />
```

### 步骤2: 验证颜色资源
确保以下颜色在 `colors.xml` 中正确定义：

```xml
<color name="purple_500">#FF6200EE</color>
<color name="purple_700">#FF3700B3</color>
<color name="light_gray">#FFF5F5F5</color>
<color name="combination_border">#FFE0E0E0</color>
```

### 步骤3: 测试按钮显示
运行应用后，您应该看到：
- **未选中按钮**: 浅灰色背景，黑色数字，灰色边框
- **选中按钮**: 紫色背景，白色数字，深紫色边框

## 🎯 预期效果

### 未选中状态：
- 背景：浅灰色 (#FFF5F5F5)
- 文字：黑色
- 边框：灰色，3px宽度
- 圆角：20dp

### 选中状态：
- 背景：紫色 (#FF6200EE)
- 文字：白色
- 边框：深紫色 (#FF3700B3)，6px宽度
- 圆角：20dp

## 🔍 故障排除

如果按钮仍然不显示正确，请检查：

### 1. 检查颜色资源
```bash
# 确保颜色资源文件存在
app/src/main/res/values/colors.xml
```

### 2. 检查导入
```kotlin
import com.lotteryfilter.app.ui.SimpleNumberSelectorView
```

### 3. 清理并重新构建
```bash
./gradlew clean
./gradlew build
```

### 4. 检查设备主题
某些设备的深色主题可能影响显示，可以在应用中强制使用浅色主题。

## 🎨 进一步优化

如果基本显示正常，可以考虑以下优化：

### 1. 添加动画效果
```kotlin
// 添加选择动画
button.animate()
    .scaleX(if (isSelected) 1.1f else 1.0f)
    .scaleY(if (isSelected) 1.1f else 1.0f)
    .setDuration(200)
    .start()
```

### 2. 添加触摸反馈
```kotlin
// 添加触摸波纹效果
button.foreground = ContextCompat.getDrawable(context, 
    android.R.drawable.list_selector_background)
```

### 3. 优化网格布局
```kotlin
// 动态计算按钮大小
val screenWidth = resources.displayMetrics.widthPixels
val buttonSize = (screenWidth - padding * 2) / 7 - margin * 2
```

## 📱 测试建议

1. **在不同设备上测试** - 确保在不同屏幕尺寸和密度下正常显示
2. **测试深色主题** - 验证在深色主题下的显示效果
3. **测试触摸响应** - 确保按钮点击响应正常
4. **测试滚动性能** - 在包含大量按钮时测试滚动性能

现在的实现应该能够正确显示数字按钮。如果还有问题，请告诉我具体的错误信息或截图，我可以进一步协助解决。
