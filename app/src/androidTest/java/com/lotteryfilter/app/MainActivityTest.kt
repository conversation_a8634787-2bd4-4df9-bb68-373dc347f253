package com.lotteryfilter.app

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.*
import androidx.test.espresso.assertion.ViewAssertions.*
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class MainActivityTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testUIElementsAreDisplayed() {
        // Check that main UI elements are displayed
        onView(withId(R.id.etExcludeCombinations)).check(matches(isDisplayed()))
        onView(withId(R.id.etExcludeIndividual)).check(matches(isDisplayed()))
        onView(withId(R.id.btnGenerate)).check(matches(isDisplayed()))
        onView(withId(R.id.btnClear)).check(matches(isDisplayed()))
    }

    @Test
    fun testClearButtonFunctionality() {
        // Enter some text
        onView(withId(R.id.etExcludeCombinations)).perform(typeText("1,2,3,4,5"))
        onView(withId(R.id.etExcludeIndividual)).perform(typeText("15,20"))
        
        // Close keyboard
        closeSoftKeyboard()
        
        // Click clear button
        onView(withId(R.id.btnClear)).perform(click())
        
        // Check that fields are cleared
        onView(withId(R.id.etExcludeCombinations)).check(matches(withText("")))
        onView(withId(R.id.etExcludeIndividual)).check(matches(withText("")))
    }

    @Test
    fun testInputValidation() {
        // Enter invalid numbers
        onView(withId(R.id.etExcludeIndividual)).perform(typeText("36,37,38"))
        closeSoftKeyboard()
        
        // Click generate button
        onView(withId(R.id.btnGenerate)).perform(click())
        
        // Should show error (toast or other indication)
        // Note: Testing toasts requires additional setup, so we'll just verify the button is clickable
        onView(withId(R.id.btnGenerate)).check(matches(isClickable()))
    }

    @Test
    fun testValidInputProcessing() {
        // Enter valid input
        onView(withId(R.id.etExcludeIndividual)).perform(typeText("35"))
        closeSoftKeyboard()
        
        // Click generate button
        onView(withId(R.id.btnGenerate)).perform(click())
        
        // Should show progress indicator
        onView(withId(R.id.progressBar)).check(matches(isDisplayed()))
    }
}
