package com.lotteryfilter.app.model

import org.junit.Assert.*
import org.junit.Test

class FilterCriteriaTest {

    @Test
    fun `empty filter criteria should be valid`() {
        val criteria = FilterCriteria()
        assertTrue(criteria.isValid())
    }

    @Test
    fun `filter criteria with valid numbers should be valid`() {
        val criteria = FilterCriteria(
            excludedCombinationNumbers = setOf(1, 2, 3, 4, 5),
            excludedIndividualNumbers = setOf(10, 15, 20)
        )
        assertTrue(criteria.isValid())
    }

    @Test
    fun `filter criteria with invalid numbers should be invalid`() {
        val criteria = FilterCriteria(
            excludedCombinationNumbers = setOf(1, 2, 3, 4, 36), // 36 is invalid
            excludedIndividualNumbers = setOf(10, 15, 20)
        )
        assertFalse(criteria.isValid())
    }

    @Test
    fun `shouldExcludeCombination should exclude when combination contains individual excluded numbers`() {
        val criteria = FilterCriteria(
            excludedIndividualNumbers = setOf(15, 25)
        )
        val combination = LotteryCombination(listOf(1, 5, 10, 15, 20))
        
        assertTrue(criteria.shouldExcludeCombination(combination))
    }

    @Test
    fun `shouldExcludeCombination should exclude when combination can be formed from excluded combination numbers`() {
        val criteria = FilterCriteria(
            excludedCombinationNumbers = setOf(1, 2, 3, 4, 5, 6, 7, 8, 9)
        )
        val combination = LotteryCombination(listOf(1, 2, 3, 4, 5))
        
        assertTrue(criteria.shouldExcludeCombination(combination))
    }

    @Test
    fun `shouldExcludeCombination should not exclude valid combinations`() {
        val criteria = FilterCriteria(
            excludedCombinationNumbers = setOf(1, 2, 3, 4, 5),
            excludedIndividualNumbers = setOf(25, 30)
        )
        val combination = LotteryCombination(listOf(10, 15, 20, 31, 35))
        
        assertFalse(criteria.shouldExcludeCombination(combination))
    }

    @Test
    fun `getSummary should return correct summary for empty criteria`() {
        val criteria = FilterCriteria()
        assertEquals("No filters applied", criteria.getSummary())
    }

    @Test
    fun `getSummary should return correct summary for populated criteria`() {
        val criteria = FilterCriteria(
            excludedCombinationNumbers = setOf(3, 1, 2),
            excludedIndividualNumbers = setOf(20, 15)
        )
        val summary = criteria.getSummary()
        
        assertTrue(summary.contains("Excluded combination numbers: 1, 2, 3"))
        assertTrue(summary.contains("Excluded individual numbers: 15, 20"))
    }
}
