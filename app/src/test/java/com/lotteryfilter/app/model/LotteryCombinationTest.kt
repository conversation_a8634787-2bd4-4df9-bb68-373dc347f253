package com.lotteryfilter.app.model

import org.junit.Assert.*
import org.junit.Test

class LotteryCombinationTest {

    @Test
    fun `combination creation with valid numbers should succeed`() {
        val numbers = listOf(1, 5, 10, 15, 20)
        val combination = LotteryCombination(numbers)
        
        assertEquals(numbers.sorted(), combination.sortedNumbers)
        assertEquals("1, 5, 10, 15, 20", combination.toString())
    }

    @Test(expected = IllegalArgumentException::class)
    fun `combination creation with wrong size should fail`() {
        val numbers = listOf(1, 2, 3, 4) // Only 4 numbers
        LotteryCombination(numbers)
    }

    @Test(expected = IllegalArgumentException::class)
    fun `combination creation with invalid numbers should fail`() {
        val numbers = listOf(1, 2, 3, 4, 36) // 36 is out of range
        LotteryCombination(numbers)
    }

    @Test(expected = IllegalArgumentException::class)
    fun `combination creation with duplicate numbers should fail`() {
        val numbers = listOf(1, 2, 3, 4, 4) // Duplicate 4
        LotteryCombination(numbers)
    }

    @Test
    fun `containsAny should return true when combination contains excluded numbers`() {
        val combination = LotteryCombination(listOf(1, 5, 10, 15, 20))
        val excludedNumbers = setOf(5, 25, 30)
        
        assertTrue(combination.containsAny(excludedNumbers))
    }

    @Test
    fun `containsAny should return false when combination does not contain excluded numbers`() {
        val combination = LotteryCombination(listOf(1, 5, 10, 15, 20))
        val excludedNumbers = setOf(25, 30, 35)
        
        assertFalse(combination.containsAny(excludedNumbers))
    }

    @Test
    fun `canBeFormedFrom should return true when all numbers are available`() {
        val combination = LotteryCombination(listOf(1, 5, 10, 15, 20))
        val availableNumbers = setOf(1, 2, 3, 4, 5, 10, 15, 20, 25)
        
        assertTrue(combination.canBeFormedFrom(availableNumbers))
    }

    @Test
    fun `canBeFormedFrom should return false when some numbers are not available`() {
        val combination = LotteryCombination(listOf(1, 5, 10, 15, 20))
        val availableNumbers = setOf(1, 2, 3, 4, 5, 10, 15) // Missing 20
        
        assertFalse(combination.canBeFormedFrom(availableNumbers))
    }

    @Test
    fun `sortedNumbers should always return numbers in ascending order`() {
        val combination = LotteryCombination(listOf(20, 1, 15, 5, 10))
        val expected = listOf(1, 5, 10, 15, 20)
        
        assertEquals(expected, combination.sortedNumbers)
    }
}
