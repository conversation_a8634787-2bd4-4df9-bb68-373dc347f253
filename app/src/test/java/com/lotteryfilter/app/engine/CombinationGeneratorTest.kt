package com.lotteryfilter.app.engine

import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Test

class CombinationGeneratorTest {

    private val generator = CombinationGenerator()

    @Test
    fun `calculateTotalCombinations should return correct value for C(35,5)`() {
        val total = generator.calculateTotalCombinations()
        assertEquals(324632L, total)
    }

    @Test
    fun `calculateTotalCombinations should handle edge cases`() {
        assertEquals(1L, generator.calculateTotalCombinations(5, 0))
        assertEquals(1L, generator.calculateTotalCombinations(5, 5))
        assertEquals(0L, generator.calculateTotalCombinations(3, 5))
    }

    @Test
    fun `generateAllCombinations should start with correct first combination`() = runTest {
        val firstCombination = generator.generateAllCombinations().take(1).toList().first()
        assertEquals(listOf(1, 2, 3, 4, 5), firstCombination.sortedNumbers)
    }

    @Test
    fun `generateAllCombinations should generate unique combinations`() = runTest {
        val combinations = generator.generateAllCombinations().take(100).toList()
        val uniqueCombinations = combinations.map { it.sortedNumbers }.toSet()
        
        assertEquals(combinations.size, uniqueCombinations.size)
    }

    @Test
    fun `generateAllCombinations should generate valid combinations`() = runTest {
        val combinations = generator.generateAllCombinations().take(50).toList()
        
        combinations.forEach { combination ->
            // Check size
            assertEquals(5, combination.numbers.size)
            
            // Check range
            assertTrue(combination.numbers.all { it in 1..35 })
            
            // Check uniqueness
            assertEquals(5, combination.numbers.distinct().size)
            
            // Check sorted order
            assertEquals(combination.numbers.sorted(), combination.sortedNumbers)
        }
    }

    @Test
    fun `generateCombinationsBatch should return correct batch sizes`() = runTest {
        val batchSize = 10
        val batches = generator.generateCombinationsBatch(batchSize).take(5).toList()
        
        // All batches except possibly the last should have the correct size
        batches.dropLast(1).forEach { batch ->
            assertEquals(batchSize, batch.size)
        }
        
        // Last batch should have size <= batchSize
        assertTrue(batches.last().size <= batchSize)
    }

    @Test
    fun `combinations should be in lexicographic order`() = runTest {
        val combinations = generator.generateAllCombinations().take(10).toList()
        
        for (i in 1 until combinations.size) {
            val prev = combinations[i - 1].sortedNumbers
            val curr = combinations[i].sortedNumbers
            
            // Current should be lexicographically greater than previous
            assertTrue("$curr should be > $prev", curr > prev)
        }
    }
}
