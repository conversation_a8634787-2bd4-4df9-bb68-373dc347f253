package com.lotteryfilter.app.engine

import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Test

class FilterEngineTest {

    private val filterEngine = FilterEngine()

    @Test
    fun `parseNumbers should handle valid input`() {
        val result = filterEngine.parseNumbers("1,2,3,4,5")
        assertEquals(setOf(1, 2, 3, 4, 5), result)
    }

    @Test
    fun `parseNumbers should handle input with spaces`() {
        val result = filterEngine.parseNumbers("1, 2, 3, 4, 5")
        assertEquals(setOf(1, 2, 3, 4, 5), result)
    }

    @Test
    fun `parseNumbers should filter out invalid numbers`() {
        val result = filterEngine.parseNumbers("1,2,36,37,5")
        assertEquals(setOf(1, 2, 5), result)
    }

    @Test
    fun `parseNumbers should handle empty input`() {
        val result = filterEngine.parseNumbers("")
        assertEquals(emptySet<Int>(), result)
    }

    @Test
    fun `parseNumbers should handle invalid format gracefully`() {
        val result = filterEngine.parseNumbers("1,abc,3,def,5")
        assertEquals(emptySet<Int>(), result)
    }

    @Test
    fun `parseNumbers should remove duplicates`() {
        val result = filterEngine.parseNumbers("1,2,2,3,3,4,5")
        assertEquals(setOf(1, 2, 3, 4, 5), result)
    }

    @Test
    fun `createFilterCriteria should create correct criteria`() {
        val criteria = filterEngine.createFilterCriteria("1,2,3,4,5", "10,15,20")
        
        assertEquals(setOf(1, 2, 3, 4, 5), criteria.excludedCombinationNumbers)
        assertEquals(setOf(10, 15, 20), criteria.excludedIndividualNumbers)
    }

    @Test
    fun `createFilterCriteria should handle empty inputs`() {
        val criteria = filterEngine.createFilterCriteria("", "")
        
        assertEquals(emptySet<Int>(), criteria.excludedCombinationNumbers)
        assertEquals(emptySet<Int>(), criteria.excludedIndividualNumbers)
    }

    @Test
    fun `filterCombinations should exclude individual numbers correctly`() = runTest {
        val criteria = filterEngine.createFilterCriteria("", "15")
        val results = filterEngine.filterCombinations(criteria).toList()
        
        // Should have progress updates and final result
        assertTrue(results.isNotEmpty())
        
        val finalResult = results.last()
        assertTrue(finalResult is FilterResult.Complete)
        
        val combinations = (finalResult as FilterResult.Complete).combinations
        
        // No combination should contain 15
        combinations.forEach { combination ->
            assertFalse("Combination $combination should not contain 15", 
                       combination.numbers.contains(15))
        }
    }

    @Test
    fun `filterCombinations should exclude combination numbers correctly`() = runTest {
        val criteria = filterEngine.createFilterCriteria("1,2,3,4,5,6,7,8,9", "")
        val results = filterEngine.filterCombinations(criteria).toList()
        
        val finalResult = results.last()
        assertTrue(finalResult is FilterResult.Complete)
        
        val combinations = (finalResult as FilterResult.Complete).combinations
        
        // No combination should be formable from 1,2,3,4,5,6,7,8,9
        combinations.forEach { combination ->
            assertFalse("Combination $combination should not be formable from excluded numbers",
                       combination.canBeFormedFrom(setOf(1,2,3,4,5,6,7,8,9)))
        }
    }

    @Test
    fun `countFilteredCombinations should provide progress updates`() = runTest {
        val criteria = filterEngine.createFilterCriteria("", "35")
        val results = filterEngine.countFilteredCombinations(criteria).toList()
        
        // Should have multiple progress updates
        assertTrue(results.size > 1)
        
        // Should have progress results
        val progressResults = results.filterIsInstance<CountResult.Progress>()
        assertTrue(progressResults.isNotEmpty())
        
        // Final result should be complete
        val finalResult = results.last()
        assertTrue(finalResult is CountResult.Complete)
    }
}
