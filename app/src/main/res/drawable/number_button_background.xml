<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/purple_500" />
            <stroke 
                android:width="2dp" 
                android:color="@color/purple_700" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/purple_200" />
            <stroke 
                android:width="2dp" 
                android:color="@color/purple_500" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/light_gray" />
            <stroke 
                android:width="1dp" 
                android:color="@color/combination_border" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
</selector>
