<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">彩票号码过滤器</string>
    <string name="exclude_combinations_hint">点击选择要排除的组合数字</string>
    <string name="exclude_individual_hint">点击选择要排除的单个数字</string>
    <string name="generate_button">生成并过滤</string>
    <string name="clear_button">清除</string>
    <string name="clear_all_button">全部清除</string>
    <string name="total_combinations">总组合数：%d</string>
    <string name="filtered_combinations">过滤后组合数：%d</string>
    <string name="processing">正在处理组合...</string>
    <string name="no_results">没有符合过滤条件的组合</string>
    <string name="invalid_input">请选择有效的数字（1-35）</string>
    <string name="exclude_combinations_label">排除组合数字</string>
    <string name="exclude_individual_label">排除单个数字</string>
    <string name="results_label">过滤结果</string>
    <string name="selected_numbers">已选择的数字：</string>
    <string name="no_numbers_selected">未选择任何数字</string>
    <string name="number_selected">数字 %d 已选择</string>
    <string name="number_deselected">数字 %d 已取消选择</string>
    <string name="processing_progress">处理进度：%d%%</string>
    <string name="combination_count_format">第 %d 个组合</string>
    <string name="filter_description">过滤说明：从1-35中选择5个数字的所有组合（共324,632种）</string>
</resources>
