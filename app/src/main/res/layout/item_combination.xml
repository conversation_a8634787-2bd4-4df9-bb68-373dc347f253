<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/combination_background"
    app:strokeColor="@color/combination_border"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Combination Numbers -->
        <LinearLayout
            android:id="@+id/llNumbers"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="start|center_vertical">

            <!-- Number circles will be added programmatically -->

        </LinearLayout>

        <!-- Combination Text (fallback) -->
        <TextView
            android:id="@+id/tvCombination"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="1, 2, 3, 4, 5"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/purple_700"
            android:gravity="center"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
