<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/combination_background"
    app:strokeColor="@color/combination_border"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 组合标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="号码组合"
            android:textSize="12sp"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- 数字显示区域 -->
        <LinearLayout
            android:id="@+id/llNumbers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- Number circles will be added programmatically -->

        </LinearLayout>

        <!-- 备用文本显示 (隐藏) -->
        <TextView
            android:id="@+id/tvCombination"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1, 2, 3, 4, 5"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/purple_700"
            android:gravity="center"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
