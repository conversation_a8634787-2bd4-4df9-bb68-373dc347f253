<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_lottery_ball"
                android:layout_marginEnd="12dp"
                app:tint="@color/purple_700" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/purple_700" />

        </LinearLayout>

        <!-- Description -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/filter_description"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:textColor="@color/dark_gray" />

        <!-- Exclude Combination Numbers Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_numbers"
                        android:layout_marginEnd="8dp"
                        app:tint="@color/purple_500" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/exclude_combinations_label"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/purple_700" />

                </LinearLayout>

                <com.lotteryfilter.app.ui.BasicNumberSelectorView
                    android:id="@+id/numberSelectorCombinations"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Exclude Individual Numbers Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_filter"
                        android:layout_marginEnd="8dp"
                        app:tint="@color/teal_700" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/exclude_individual_label"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/teal_700" />

                </LinearLayout>

                <com.lotteryfilter.app.ui.BasicNumberSelectorView
                    android:id="@+id/numberSelectorIndividual"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnGenerate"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="@string/generate_button"
                android:layout_marginEnd="8dp"
                android:textSize="16sp"
                app:backgroundTint="@color/purple_500"
                app:icon="@drawable/ic_filter"
                app:iconGravity="textStart"
                app:iconPadding="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnClear"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="@string/clear_all_button"
                android:layout_marginStart="8dp"
                android:textSize="16sp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:strokeColor="@color/purple_500"
                app:icon="@drawable/ic_clear"
                app:iconGravity="textStart"
                app:iconPadding="8dp"
                app:iconTint="@color/purple_500" />

        </LinearLayout>

        <!-- Progress Bar -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:indicatorColor="@color/purple_500" />

        <!-- Status Text -->
        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            android:textColor="@color/dark_gray" />

        <!-- Results Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardResults"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_lottery_ball"
                        android:layout_marginEnd="8dp"
                        app:tint="@color/purple_500" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/results_label"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/purple_700" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvResultsCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp"
                    android:textColor="@color/dark_gray" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvResults"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    tools:listitem="@layout/item_combination" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
