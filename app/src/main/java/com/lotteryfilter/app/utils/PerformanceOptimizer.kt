package com.lotteryfilter.app.utils

import com.lotteryfilter.app.model.LotteryCombination
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * Performance optimization utilities for handling large datasets
 */
object PerformanceOptimizer {
    
    // Cache for frequently accessed combinations
    private val combinationCache = ConcurrentHashMap<String, List<LotteryCombination>>()
    
    // Memory management constants
    private const val MAX_CACHE_SIZE = 1000
    private const val BATCH_SIZE = 5000
    
    /**
     * Processes combinations in batches to avoid memory issues
     */
    suspend fun processCombinationsInBatches(
        combinations: List<LotteryCombination>,
        batchSize: Int = BATCH_SIZE,
        processor: suspend (List<LotteryCombination>) -> Unit
    ) = withContext(Dispatchers.Default) {
        combinations.chunked(batchSize).forEach { batch ->
            processor(batch)
        }
    }
    
    /**
     * Caches filtered results for quick retrieval
     */
    fun cacheFilteredResults(key: String, combinations: List<LotteryCombination>) {
        if (combinationCache.size >= MAX_CACHE_SIZE) {
            // Remove oldest entries when cache is full
            val keysToRemove = combinationCache.keys.take(MAX_CACHE_SIZE / 2)
            keysToRemove.forEach { combinationCache.remove(it) }
        }
        combinationCache[key] = combinations
    }
    
    /**
     * Retrieves cached results if available
     */
    fun getCachedResults(key: String): List<LotteryCombination>? {
        return combinationCache[key]
    }
    
    /**
     * Generates cache key from filter criteria
     */
    fun generateCacheKey(excludeCombinations: String, excludeIndividual: String): String {
        return "filter_${excludeCombinations.hashCode()}_${excludeIndividual.hashCode()}"
    }
    
    /**
     * Clears all cached data to free memory
     */
    fun clearCache() {
        combinationCache.clear()
    }
    
    /**
     * Gets current cache size for monitoring
     */
    fun getCacheSize(): Int = combinationCache.size
    
    /**
     * Optimized combination validation using bit operations
     */
    fun fastContainsCheck(combination: List<Int>, excludedNumbers: Set<Int>): Boolean {
        // Convert to bit mask for faster checking
        var combinationMask = 0L
        var excludedMask = 0L
        
        combination.forEach { num ->
            if (num <= 64) combinationMask = combinationMask or (1L shl (num - 1))
        }
        
        excludedNumbers.forEach { num ->
            if (num <= 64) excludedMask = excludedMask or (1L shl (num - 1))
        }
        
        return (combinationMask and excludedMask) != 0L
    }
    
    /**
     * Memory-efficient combination generation with early filtering
     */
    suspend fun generateWithEarlyFiltering(
        excludedCombinationNumbers: Set<Int>,
        excludedIndividualNumbers: Set<Int>,
        onProgress: (Int, Int) -> Unit,
        onBatch: suspend (List<LotteryCombination>) -> Unit
    ) = withContext(Dispatchers.Default) {
        
        val batch = mutableListOf<LotteryCombination>()
        var processed = 0
        var filtered = 0
        
        // Generate combinations with early filtering
        for (a in 1..31) {
            if (a in excludedIndividualNumbers) continue
            
            for (b in (a + 1)..32) {
                if (b in excludedIndividualNumbers) continue
                
                for (c in (b + 1)..33) {
                    if (c in excludedIndividualNumbers) continue
                    
                    for (d in (c + 1)..34) {
                        if (d in excludedIndividualNumbers) continue
                        
                        for (e in (d + 1)..35) {
                            if (e in excludedIndividualNumbers) continue
                            
                            processed++
                            
                            val combination = listOf(a, b, c, d, e)
                            
                            // Early filtering check
                            val shouldExclude = excludedCombinationNumbers.isNotEmpty() && 
                                               combination.all { it in excludedCombinationNumbers }
                            
                            if (!shouldExclude) {
                                batch.add(LotteryCombination(combination))
                                filtered++
                                
                                if (batch.size >= BATCH_SIZE) {
                                    onBatch(batch.toList())
                                    batch.clear()
                                }
                            }
                            
                            if (processed % 10000 == 0) {
                                onProgress(processed, filtered)
                            }
                        }
                    }
                }
            }
        }
        
        // Process remaining batch
        if (batch.isNotEmpty()) {
            onBatch(batch.toList())
        }
        
        onProgress(processed, filtered)
    }
}
