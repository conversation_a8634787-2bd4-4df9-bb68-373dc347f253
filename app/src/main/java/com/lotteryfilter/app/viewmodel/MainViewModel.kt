package com.lotteryfilter.app.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.lotteryfilter.app.engine.FilterEngine
import com.lotteryfilter.app.engine.FilterResult
import com.lotteryfilter.app.model.LotteryCombination
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class MainViewModel : ViewModel() {
    
    private val filterEngine = FilterEngine()
    private var currentJob: Job? = null
    
    private val _uiState = MutableStateFlow<UiState>(UiState.Idle)
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    /**
     * Generates and filters combinations based on user input
     */
    fun generateAndFilterCombinations(excludeCombinationsInput: String, excludeIndividualInput: String) {
        // Cancel any existing operation
        currentJob?.cancel()
        
        currentJob = viewModelScope.launch {
            try {
                _uiState.value = UiState.Loading
                
                val criteria = filterEngine.createFilterCriteria(
                    excludeCombinationsInput,
                    excludeIndividualInput
                )
                
                if (!criteria.isValid()) {
                    _uiState.value = UiState.Error("Invalid input: numbers must be between 1 and 35")
                    return@launch
                }
                
                filterEngine.filterCombinations(criteria).collect { result ->
                    when (result) {
                        is FilterResult.Progress -> {
                            _uiState.value = UiState.Progress(
                                processed = result.processed,
                                filtered = result.filtered,
                                total = result.total
                            )
                        }
                        
                        is FilterResult.Complete -> {
                            if (result.combinations.isEmpty()) {
                                _uiState.value = UiState.Error("No combinations match your filters")
                            } else {
                                _uiState.value = UiState.Success(
                                    combinations = result.combinations,
                                    totalProcessed = result.totalProcessed,
                                    totalFiltered = result.totalFiltered
                                )
                            }
                        }
                    }
                }
                
            } catch (e: Exception) {
                _uiState.value = UiState.Error("Error processing combinations: ${e.message}")
            }
        }
    }
    
    /**
     * Clears all results and resets to idle state
     */
    fun clearResults() {
        currentJob?.cancel()
        _uiState.value = UiState.Idle
    }
    
    override fun onCleared() {
        super.onCleared()
        currentJob?.cancel()
    }
    
    /**
     * Sealed class representing different UI states
     */
    sealed class UiState {
        object Idle : UiState()
        object Loading : UiState()
        
        data class Progress(
            val processed: Int,
            val filtered: Int,
            val total: Int
        ) : UiState()
        
        data class Success(
            val combinations: List<LotteryCombination>,
            val totalProcessed: Int,
            val totalFiltered: Int
        ) : UiState()
        
        data class Error(val message: String) : UiState()
    }
}
