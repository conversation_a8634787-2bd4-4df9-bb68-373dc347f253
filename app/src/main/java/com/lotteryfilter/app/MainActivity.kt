package com.lotteryfilter.app

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.lotteryfilter.app.databinding.ActivityMainBinding
import com.lotteryfilter.app.ui.CombinationAdapter
import com.lotteryfilter.app.ui.BasicNumberSelectorView
import com.lotteryfilter.app.viewmodel.MainViewModel
import kotlinx.coroutines.launch

/**
 * 主活动 - 彩票号码过滤器
 * Main Activity for Lottery Number Filter
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var viewModel: MainViewModel
    private lateinit var adapter: CombinationAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViewModel()
        setupRecyclerView()
        setupNumberSelectors()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]
    }
    
    private fun setupRecyclerView() {
        adapter = CombinationAdapter()
        binding.rvResults.layoutManager = LinearLayoutManager(this)
        binding.rvResults.adapter = adapter
    }

    /**
     * 设置数字选择器
     * Setup number selectors
     */
    private fun setupNumberSelectors() {
        // 设置组合数字选择器标题
        binding.numberSelectorCombinations.setTitle(getString(R.string.exclude_combinations_hint))

        // 设置单个数字选择器标题
        binding.numberSelectorIndividual.setTitle(getString(R.string.exclude_individual_hint))

        // 设置选择变化监听器
        binding.numberSelectorCombinations.onSelectionChanged = { selectedNumbers ->
            // 可以在这里添加实时反馈
            if (selectedNumbers.isNotEmpty()) {
                showToast(getString(R.string.selected_numbers) + selectedNumbers.sorted().joinToString(", "))
            }
        }

        binding.numberSelectorIndividual.onSelectionChanged = { selectedNumbers ->
            // 可以在这里添加实时反馈
            if (selectedNumbers.isNotEmpty()) {
                showToast(getString(R.string.selected_numbers) + selectedNumbers.sorted().joinToString(", "))
            }
        }
    }

    private fun setupClickListeners() {
        binding.btnGenerate.setOnClickListener {
            val excludeCombinations = binding.numberSelectorCombinations.getSelectedNumbers()
            val excludeIndividual = binding.numberSelectorIndividual.getSelectedNumbers()

            if (validateInput(excludeCombinations, excludeIndividual)) {
                // 转换为字符串格式以兼容现有的ViewModel
                val excludeCombinationsStr = excludeCombinations.sorted().joinToString(",")
                val excludeIndividualStr = excludeIndividual.sorted().joinToString(",")

                viewModel.generateAndFilterCombinations(excludeCombinationsStr, excludeIndividualStr)
            }
        }

        binding.btnClear.setOnClickListener {
            clearInputs()
            viewModel.clearResults()
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
    }
    
    private fun updateUI(state: MainViewModel.UiState) {
        when (state) {
            is MainViewModel.UiState.Idle -> {
                hideProgress()
                hideResults()
            }
            
            is MainViewModel.UiState.Loading -> {
                showProgress()
                binding.tvStatus.text = getString(R.string.processing)
                binding.tvStatus.visibility = View.VISIBLE
                hideResults()
            }
            
            is MainViewModel.UiState.Progress -> {
                showProgress()
                val progressPercent = (state.processed * 100) / state.total
                binding.progressBar.progress = progressPercent
                binding.tvStatus.text = "处理进度: ${state.processed}/${state.total} (已过滤: ${state.filtered})"
                binding.tvStatus.visibility = View.VISIBLE
            }

            is MainViewModel.UiState.Success -> {
                hideProgress()
                showResults(state.combinations.size, state.totalProcessed)
                adapter.submitList(state.combinations)
                showToast("过滤完成！找到 ${state.combinations.size} 个符合条件的组合")
            }

            is MainViewModel.UiState.Error -> {
                hideProgress()
                hideResults()
                showToast(state.message)
            }
        }
    }
    
    /**
     * 验证输入的数字
     * Validate input numbers
     */
    private fun validateInput(excludeCombinations: Set<Int>, excludeIndividual: Set<Int>): Boolean {
        val allNumbers = excludeCombinations + excludeIndividual
        val invalidNumbers = allNumbers.filter { it !in 1..35 }

        if (invalidNumbers.isNotEmpty()) {
            showToast(getString(R.string.invalid_input))
            return false
        }

        // 检查是否至少选择了一些数字进行过滤
        if (excludeCombinations.isEmpty() && excludeIndividual.isEmpty()) {
            showToast("请至少选择一些要排除的数字")
            return false
        }

        return true
    }

    /**
     * 清除所有输入
     * Clear all inputs
     */
    private fun clearInputs() {
        binding.numberSelectorCombinations.clearSelection()
        binding.numberSelectorIndividual.clearSelection()
    }

    /**
     * 显示Toast消息
     * Show toast message
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    private fun showProgress() {
        binding.progressBar.visibility = View.VISIBLE
        binding.btnGenerate.isEnabled = false
    }
    
    private fun hideProgress() {
        binding.progressBar.visibility = View.GONE
        binding.tvStatus.visibility = View.GONE
        binding.btnGenerate.isEnabled = true
    }
    
    private fun showResults(filteredCount: Int, totalCount: Int) {
        binding.cardResults.visibility = View.VISIBLE
        binding.tvResultsCount.text = getString(R.string.filtered_combinations, filteredCount)
        binding.tvResultsCount.append("\n${getString(R.string.total_combinations, totalCount)}")
    }
    
    private fun hideResults() {
        binding.cardResults.visibility = View.GONE
    }
}
