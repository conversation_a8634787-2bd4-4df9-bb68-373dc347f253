package com.lotteryfilter.app

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.lotteryfilter.app.databinding.ActivityMainBinding
import com.lotteryfilter.app.ui.CombinationAdapter
import com.lotteryfilter.app.viewmodel.MainViewModel
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var viewModel: MainViewModel
    private lateinit var adapter: CombinationAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViewModel()
        setupRecyclerView()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]
    }
    
    private fun setupRecyclerView() {
        adapter = CombinationAdapter()
        binding.rvResults.layoutManager = LinearLayoutManager(this)
        binding.rvResults.adapter = adapter
    }
    
    private fun setupClickListeners() {
        binding.btnGenerate.setOnClickListener {
            val excludeCombinations = binding.etExcludeCombinations.text.toString()
            val excludeIndividual = binding.etExcludeIndividual.text.toString()
            
            if (validateInput(excludeCombinations, excludeIndividual)) {
                viewModel.generateAndFilterCombinations(excludeCombinations, excludeIndividual)
            }
        }
        
        binding.btnClear.setOnClickListener {
            clearInputs()
            viewModel.clearResults()
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
    }
    
    private fun updateUI(state: MainViewModel.UiState) {
        when (state) {
            is MainViewModel.UiState.Idle -> {
                hideProgress()
                hideResults()
            }
            
            is MainViewModel.UiState.Loading -> {
                showProgress()
                binding.tvStatus.text = getString(R.string.processing)
                binding.tvStatus.visibility = View.VISIBLE
                hideResults()
            }
            
            is MainViewModel.UiState.Progress -> {
                showProgress()
                val progressPercent = (state.processed * 100) / state.total
                binding.progressBar.progress = progressPercent
                binding.tvStatus.text = "Processing: ${state.processed}/${state.total} (${state.filtered} filtered)"
                binding.tvStatus.visibility = View.VISIBLE
            }
            
            is MainViewModel.UiState.Success -> {
                hideProgress()
                showResults(state.combinations.size, state.totalProcessed)
                adapter.submitList(state.combinations)
            }
            
            is MainViewModel.UiState.Error -> {
                hideProgress()
                hideResults()
                Toast.makeText(this, state.message, Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun validateInput(excludeCombinations: String, excludeIndividual: String): Boolean {
        // Basic validation - check if numbers are in valid range
        val combinationNumbers = parseNumbers(excludeCombinations)
        val individualNumbers = parseNumbers(excludeIndividual)
        
        val allNumbers = combinationNumbers + individualNumbers
        val invalidNumbers = allNumbers.filter { it !in 1..35 }
        
        if (invalidNumbers.isNotEmpty()) {
            Toast.makeText(this, getString(R.string.invalid_input), Toast.LENGTH_SHORT).show()
            return false
        }
        
        return true
    }
    
    private fun parseNumbers(input: String): List<Int> {
        if (input.isBlank()) return emptyList()
        
        return try {
            input.split(",")
                .map { it.trim() }
                .filter { it.isNotEmpty() }
                .map { it.toInt() }
        } catch (e: NumberFormatException) {
            emptyList()
        }
    }
    
    private fun clearInputs() {
        binding.etExcludeCombinations.text?.clear()
        binding.etExcludeIndividual.text?.clear()
    }
    
    private fun showProgress() {
        binding.progressBar.visibility = View.VISIBLE
        binding.btnGenerate.isEnabled = false
    }
    
    private fun hideProgress() {
        binding.progressBar.visibility = View.GONE
        binding.tvStatus.visibility = View.GONE
        binding.btnGenerate.isEnabled = true
    }
    
    private fun showResults(filteredCount: Int, totalCount: Int) {
        binding.cardResults.visibility = View.VISIBLE
        binding.tvResultsCount.text = getString(R.string.filtered_combinations, filteredCount)
        binding.tvResultsCount.append("\n${getString(R.string.total_combinations, totalCount)}")
    }
    
    private fun hideResults() {
        binding.cardResults.visibility = View.GONE
    }
}
