package com.lotteryfilter.app.ui

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.GridLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.lotteryfilter.app.R

/**
 * 自定义数字选择器视图，提供1-35的数字选择功能
 * Custom number selector view for selecting numbers 1-35
 */
class NumberSelectorView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var titleTextView: TextView
    private lateinit var selectedNumbersTextView: TextView
    private lateinit var numberGrid: GridLayout
    private lateinit var clearButton: MaterialButton
    
    private val selectedNumbers = mutableSetOf<Int>()
    private val numberButtons = mutableMapOf<Int, androidx.appcompat.widget.AppCompatButton>()
    
    // 回调函数，当选择的数字发生变化时调用
    var onSelectionChanged: ((Set<Int>) -> Unit)? = null
    
    init {
        orientation = VERTICAL
        setupView()
    }
    
    private fun setupView() {
        // 创建标题
        titleTextView = TextView(context).apply {
            textSize = 16f
            setTextColor(ContextCompat.getColor(context, R.color.purple_700))
            setPadding(0, 0, 0, 16)
        }
        addView(titleTextView)
        
        // 创建已选择数字显示区域
        selectedNumbersTextView = TextView(context).apply {
            textSize = 14f
            setTextColor(ContextCompat.getColor(context, R.color.dark_gray))
            setPadding(16, 8, 16, 16)
            background = ContextCompat.getDrawable(context, R.drawable.selected_numbers_background)
            text = context.getString(R.string.no_numbers_selected)
        }
        addView(selectedNumbersTextView)
        
        // 创建数字网格
        numberGrid = GridLayout(context).apply {
            columnCount = 7 // 7列显示数字1-35
            setPadding(8, 16, 8, 16)
        }
        addView(numberGrid)
        
        // 创建清除按钮
        clearButton = MaterialButton(context).apply {
            text = context.getString(R.string.clear_button)
            setOnClickListener { clearSelection() }
            layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                topMargin = 16
            }
        }
        addView(clearButton)
        
        createNumberButtons()
        updateSelectedNumbersDisplay()
    }
    
    private fun createNumberButtons() {
        for (number in 1..35) {
            // 使用普通Button而不是MaterialButton，避免样式冲突
            val button = androidx.appcompat.widget.AppCompatButton(context).apply {
                text = number.toString()

                // 设置布局参数
                val size = context.resources.getDimensionPixelSize(R.dimen.number_button_size)
                layoutParams = GridLayout.LayoutParams().apply {
                    width = size
                    height = size
                    columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f)
                    setMargins(4, 4, 4, 4)
                }

                // 设置文字样式
                textSize = 14f
                setTextColor(ContextCompat.getColor(context, R.color.black))

                // 设置初始背景
                background = ContextCompat.getDrawable(context, R.drawable.number_button_background)

                // 设置点击监听器
                setOnClickListener {
                    toggleNumber(number)
                }
            }

            numberButtons[number] = button
            numberGrid.addView(button)
        }
    }
    
    private fun toggleNumber(number: Int) {
        if (selectedNumbers.contains(number)) {
            // 取消选择
            selectedNumbers.remove(number)
            updateButtonAppearance(number, false)
        } else {
            // 选择数字
            selectedNumbers.add(number)
            updateButtonAppearance(number, true)
        }
        
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    private fun updateButtonAppearance(number: Int, isSelected: Boolean) {
        val button = numberButtons[number] ?: return

        if (isSelected) {
            // 选中状态：紫色背景，白色文字
            button.background = ContextCompat.getDrawable(context, R.drawable.number_button_selected)
            button.setTextColor(ContextCompat.getColor(context, R.color.white))
            button.elevation = 8f
        } else {
            // 未选中状态：使用默认背景，黑色文字
            button.background = ContextCompat.getDrawable(context, R.drawable.number_button_background)
            button.setTextColor(ContextCompat.getColor(context, R.color.black))
            button.elevation = 2f
        }
    }
    
    private fun updateSelectedNumbersDisplay() {
        if (selectedNumbers.isEmpty()) {
            selectedNumbersTextView.text = context.getString(R.string.no_numbers_selected)
        } else {
            val sortedNumbers = selectedNumbers.sorted().joinToString(", ")
            selectedNumbersTextView.text = "${context.getString(R.string.selected_numbers)}$sortedNumbers"
        }
    }
    
    /**
     * 设置标题文本
     */
    fun setTitle(title: String) {
        titleTextView.text = title
    }
    
    /**
     * 获取当前选择的数字
     */
    fun getSelectedNumbers(): Set<Int> {
        return selectedNumbers.toSet()
    }
    
    /**
     * 设置选择的数字
     */
    fun setSelectedNumbers(numbers: Set<Int>) {
        clearSelection()
        numbers.forEach { number ->
            if (number in 1..35) {
                selectedNumbers.add(number)
                updateButtonAppearance(number, true)
            }
        }
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    /**
     * 清除所有选择
     */
    fun clearSelection() {
        selectedNumbers.forEach { number ->
            updateButtonAppearance(number, false)
        }
        selectedNumbers.clear()
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
}
