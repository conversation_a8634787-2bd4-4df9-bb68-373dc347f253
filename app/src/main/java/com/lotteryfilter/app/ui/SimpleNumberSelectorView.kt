package com.lotteryfilter.app.ui

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.ViewGroup
import android.widget.Button
import android.widget.GridLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.lotteryfilter.app.R

/**
 * 简化版数字选择器 - 确保按钮能正确显示
 * Simplified number selector to ensure buttons display correctly
 */
class SimpleNumberSelectorView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var titleTextView: TextView
    private lateinit var selectedNumbersTextView: TextView
    private lateinit var numberGrid: GridLayout
    
    private val selectedNumbers = mutableSetOf<Int>()
    private val numberButtons = mutableMapOf<Int, Button>()
    
    // 回调函数
    var onSelectionChanged: ((Set<Int>) -> Unit)? = null
    
    init {
        orientation = VERTICAL
        setupView()
    }
    
    private fun setupView() {
        // 创建标题
        titleTextView = TextView(context).apply {
            textSize = 16f
            setTextColor(ContextCompat.getColor(context, R.color.purple_700))
            setPadding(0, 0, 0, 16)
            gravity = Gravity.CENTER
        }
        addView(titleTextView)
        
        // 创建已选择数字显示区域
        selectedNumbersTextView = TextView(context).apply {
            textSize = 14f
            setTextColor(ContextCompat.getColor(context, R.color.dark_gray))
            setPadding(16, 8, 16, 16)
            background = ContextCompat.getDrawable(context, R.drawable.selected_numbers_background)
            text = context.getString(R.string.no_numbers_selected)
            gravity = Gravity.CENTER
        }
        addView(selectedNumbersTextView)
        
        // 创建数字网格
        numberGrid = GridLayout(context).apply {
            columnCount = 7 // 7列显示数字1-35
            setPadding(8, 16, 8, 16)
        }
        addView(numberGrid)
        
        createNumberButtons()
        updateSelectedNumbersDisplay()
    }
    
    private fun createNumberButtons() {
        for (number in 1..35) {
            val button = Button(context).apply {
                text = number.toString()
                
                // 设置布局参数
                val buttonSize = 120 // 固定大小，避免资源问题
                layoutParams = GridLayout.LayoutParams().apply {
                    width = buttonSize
                    height = buttonSize
                    columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f)
                    setMargins(6, 6, 6, 6)
                }
                
                // 设置文字样式
                textSize = 14f
                setTextColor(Color.BLACK)
                
                // 创建初始背景
                background = createButtonBackground(false)
                
                // 设置点击监听器
                setOnClickListener {
                    toggleNumber(number)
                }
            }
            
            numberButtons[number] = button
            numberGrid.addView(button)
        }
    }
    
    private fun createButtonBackground(isSelected: Boolean): GradientDrawable {
        return GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadius = 20f
            
            if (isSelected) {
                setColor(ContextCompat.getColor(context, R.color.purple_500))
                setStroke(6, ContextCompat.getColor(context, R.color.purple_700))
            } else {
                setColor(ContextCompat.getColor(context, R.color.light_gray))
                setStroke(3, ContextCompat.getColor(context, R.color.combination_border))
            }
        }
    }
    
    private fun toggleNumber(number: Int) {
        if (selectedNumbers.contains(number)) {
            // 取消选择
            selectedNumbers.remove(number)
            updateButtonAppearance(number, false)
        } else {
            // 选择数字
            selectedNumbers.add(number)
            updateButtonAppearance(number, true)
        }
        
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    private fun updateButtonAppearance(number: Int, isSelected: Boolean) {
        val button = numberButtons[number] ?: return
        
        // 更新背景
        button.background = createButtonBackground(isSelected)
        
        // 更新文字颜色
        if (isSelected) {
            button.setTextColor(Color.WHITE)
        } else {
            button.setTextColor(Color.BLACK)
        }
    }
    
    private fun updateSelectedNumbersDisplay() {
        if (selectedNumbers.isEmpty()) {
            selectedNumbersTextView.text = context.getString(R.string.no_numbers_selected)
        } else {
            val sortedNumbers = selectedNumbers.sorted().joinToString(", ")
            selectedNumbersTextView.text = "${context.getString(R.string.selected_numbers)}$sortedNumbers"
        }
    }
    
    /**
     * 设置标题文本
     */
    fun setTitle(title: String) {
        titleTextView.text = title
    }
    
    /**
     * 获取当前选择的数字
     */
    fun getSelectedNumbers(): Set<Int> {
        return selectedNumbers.toSet()
    }
    
    /**
     * 设置选择的数字
     */
    fun setSelectedNumbers(numbers: Set<Int>) {
        clearSelection()
        numbers.forEach { number ->
            if (number in 1..35) {
                selectedNumbers.add(number)
                updateButtonAppearance(number, true)
            }
        }
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    /**
     * 清除所有选择
     */
    fun clearSelection() {
        selectedNumbers.forEach { number ->
            updateButtonAppearance(number, false)
        }
        selectedNumbers.clear()
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
}
