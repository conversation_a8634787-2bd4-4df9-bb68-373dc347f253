package com.lotteryfilter.app.ui

import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.lotteryfilter.app.R
import com.lotteryfilter.app.databinding.ItemCombinationBinding
import com.lotteryfilter.app.model.LotteryCombination

/**
 * RecyclerView adapter for displaying lottery combinations with efficient updates
 */
class CombinationAdapter : ListAdapter<LotteryCombination, CombinationAdapter.CombinationViewHolder>(DiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CombinationViewHolder {
        val binding = ItemCombinationBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CombinationViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: CombinationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class CombinationViewHolder(private val binding: ItemCombinationBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(combination: LotteryCombination) {
            // Clear previous number views
            binding.llNumbers.removeAllViews()
            
            // Create circular number views
            combination.sortedNumbers.forEach { number ->
                val numberView = createNumberView(number)
                binding.llNumbers.addView(numberView)
            }
            
            // Set fallback text (hidden by default)
            binding.tvCombination.text = combination.toString()
        }
        
        private fun createNumberView(number: Int): TextView {
            val context = binding.root.context
            val textView = TextView(context)
            
            // Set text and styling
            textView.text = number.toString()
            textView.textSize = 14f
            textView.setTextColor(ContextCompat.getColor(context, android.R.color.white))
            
            // Create circular background
            val drawable = GradientDrawable().apply {
                shape = GradientDrawable.OVAL
                setColor(getNumberColor(number, context))
            }
            textView.background = drawable
            
            // Set layout parameters for circular appearance
            val size = context.resources.getDimensionPixelSize(R.dimen.number_circle_size)
            val margin = context.resources.getDimensionPixelSize(R.dimen.number_circle_margin)
            
            val layoutParams = ViewGroup.MarginLayoutParams(size, size).apply {
                setMargins(margin, 0, margin, 0)
            }
            textView.layoutParams = layoutParams
            
            // Center text
            textView.gravity = android.view.Gravity.CENTER
            
            return textView
        }
        
        private fun getNumberColor(number: Int, context: android.content.Context): Int {
            // Color coding based on number ranges for better visualization
            return when {
                number <= 7 -> ContextCompat.getColor(context, R.color.number_range_1)
                number <= 14 -> ContextCompat.getColor(context, R.color.number_range_2)
                number <= 21 -> ContextCompat.getColor(context, R.color.number_range_3)
                number <= 28 -> ContextCompat.getColor(context, R.color.number_range_4)
                else -> ContextCompat.getColor(context, R.color.number_range_5)
            }
        }
    }
    
    private class DiffCallback : DiffUtil.ItemCallback<LotteryCombination>() {
        override fun areItemsTheSame(oldItem: LotteryCombination, newItem: LotteryCombination): Boolean {
            return oldItem.sortedNumbers == newItem.sortedNumbers
        }
        
        override fun areContentsTheSame(oldItem: LotteryCombination, newItem: LotteryCombination): Boolean {
            return oldItem == newItem
        }
    }
}
