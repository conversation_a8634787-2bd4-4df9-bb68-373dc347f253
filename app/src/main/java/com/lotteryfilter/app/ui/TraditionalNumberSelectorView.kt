package com.lotteryfilter.app.ui

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.lotteryfilter.app.R

/**
 * 传统稳定版数字选择器 - 使用最基础的实现确保稳定性
 * Traditional stable number selector using basic implementation for reliability
 */
class TraditionalNumberSelectorView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var titleTextView: TextView
    private lateinit var selectedNumbersTextView: TextView
    private lateinit var numbersContainer: LinearLayout
    
    private val selectedNumbers = mutableSetOf<Int>()
    private val numberButtons = mutableMapOf<Int, Button>()
    
    // 回调函数
    var onSelectionChanged: ((Set<Int>) -> Unit)? = null
    
    init {
        orientation = VERTICAL
        setupView()
    }
    
    private fun setupView() {
        // 创建标题
        titleTextView = TextView(context).apply {
            text = "选择要排除的数字"
            textSize = 16f
            setTextColor(Color.parseColor("#FF3700B3")) // 直接使用颜色值
            setPadding(0, 0, 0, dpToPx(16))
            gravity = Gravity.CENTER
        }
        addView(titleTextView)
        
        // 创建已选择数字显示区域
        selectedNumbersTextView = TextView(context).apply {
            text = "未选择任何数字"
            textSize = 14f
            setTextColor(Color.parseColor("#FF757575"))
            setPadding(dpToPx(16), dpToPx(8), dpToPx(16), dpToPx(16))
            setBackgroundColor(Color.parseColor("#FFF8F9FA"))
            gravity = Gravity.CENTER
        }
        addView(selectedNumbersTextView)
        
        // 创建数字按钮容器
        numbersContainer = LinearLayout(context).apply {
            orientation = VERTICAL
            setPadding(dpToPx(8), dpToPx(16), dpToPx(8), dpToPx(16))
        }
        addView(numbersContainer)
        
        createNumberButtons()
        updateSelectedNumbersDisplay()
    }
    
    private fun createNumberButtons() {
        // 创建5行，每行7个按钮
        for (row in 0 until 5) {
            val rowLayout = LinearLayout(context).apply {
                orientation = HORIZONTAL
                gravity = Gravity.CENTER
                layoutParams = LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(0, dpToPx(4), 0, dpToPx(4))
                }
            }
            
            for (col in 0 until 7) {
                val number = row * 7 + col + 1
                if (number <= 35) {
                    val button = createNumberButton(number)
                    numberButtons[number] = button
                    rowLayout.addView(button)
                }
            }
            
            numbersContainer.addView(rowLayout)
        }
    }
    
    private fun createNumberButton(number: Int): Button {
        return Button(context).apply {
            text = number.toString()
            
            // 设置固定尺寸
            val buttonSize = dpToPx(48)
            layoutParams = LinearLayout.LayoutParams(buttonSize, buttonSize).apply {
                setMargins(dpToPx(3), dpToPx(3), dpToPx(3), dpToPx(3))
            }
            
            // 设置文字样式
            textSize = 14f
            setTextColor(Color.BLACK)
            
            // 设置背景色 - 使用最基础的方法
            setBackgroundColor(Color.parseColor("#FFF5F5F5")) // 浅灰色
            
            // 设置点击监听器
            setOnClickListener {
                toggleNumber(number)
            }
        }
    }
    
    private fun toggleNumber(number: Int) {
        if (selectedNumbers.contains(number)) {
            // 取消选择
            selectedNumbers.remove(number)
            updateButtonAppearance(number, false)
        } else {
            // 选择数字
            selectedNumbers.add(number)
            updateButtonAppearance(number, true)
        }
        
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    private fun updateButtonAppearance(number: Int, isSelected: Boolean) {
        val button = numberButtons[number] ?: return
        
        if (isSelected) {
            // 选中状态：紫色背景，白色文字
            button.setBackgroundColor(Color.parseColor("#FF6200EE"))
            button.setTextColor(Color.WHITE)
        } else {
            // 未选中状态：浅灰色背景，黑色文字
            button.setBackgroundColor(Color.parseColor("#FFF5F5F5"))
            button.setTextColor(Color.BLACK)
        }
    }
    
    private fun updateSelectedNumbersDisplay() {
        if (selectedNumbers.isEmpty()) {
            selectedNumbersTextView.text = "未选择任何数字"
        } else {
            val sortedNumbers = selectedNumbers.sorted().joinToString(", ")
            selectedNumbersTextView.text = "已选择的数字：$sortedNumbers"
        }
    }
    
    /**
     * 设置标题文本
     */
    fun setTitle(title: String) {
        titleTextView.text = title
    }
    
    /**
     * 获取当前选择的数字
     */
    fun getSelectedNumbers(): Set<Int> {
        return selectedNumbers.toSet()
    }
    
    /**
     * 设置选择的数字
     */
    fun setSelectedNumbers(numbers: Set<Int>) {
        clearSelection()
        numbers.forEach { number ->
            if (number in 1..35) {
                selectedNumbers.add(number)
                updateButtonAppearance(number, true)
            }
        }
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    /**
     * 清除所有选择
     */
    fun clearSelection() {
        selectedNumbers.forEach { number ->
            updateButtonAppearance(number, false)
        }
        selectedNumbers.clear()
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    /**
     * 将dp转换为px
     */
    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            context.resources.displayMetrics
        ).toInt()
    }
}
