package com.lotteryfilter.app.ui

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView

/**
 * 最基础的数字选择器 - 零依赖，确保100%稳定显示
 * Most basic number selector - zero dependencies, ensures 100% stable display
 */
class BasicNumberSelectorView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var titleTextView: TextView
    private lateinit var selectedNumbersTextView: TextView
    private lateinit var numbersContainer: LinearLayout
    
    private val selectedNumbers = mutableSetOf<Int>()
    private val numberButtons = mutableMapOf<Int, Button>()
    
    // 回调函数
    var onSelectionChanged: ((Set<Int>) -> Unit)? = null
    
    // 颜色常量 - 直接定义，避免资源依赖
    companion object {
        private const val COLOR_PURPLE = "#FF6200EE"
        private const val COLOR_PURPLE_DARK = "#FF3700B3"
        private const val COLOR_LIGHT_GRAY = "#FFF5F5F5"
        private const val COLOR_DARK_GRAY = "#FF757575"
        private const val COLOR_BACKGROUND = "#FFF8F9FA"
    }
    
    init {
        orientation = VERTICAL
        setupView()
    }
    
    private fun setupView() {
        // 创建标题
        titleTextView = TextView(context).apply {
            text = "选择要排除的数字"
            textSize = 16f
            setTextColor(Color.parseColor(COLOR_PURPLE_DARK))
            setPadding(0, 0, 0, dpToPx(16))
            gravity = Gravity.CENTER
        }
        addView(titleTextView)
        
        // 创建已选择数字显示区域
        selectedNumbersTextView = TextView(context).apply {
            text = "未选择任何数字"
            textSize = 14f
            setTextColor(Color.parseColor(COLOR_DARK_GRAY))
            setPadding(dpToPx(16), dpToPx(8), dpToPx(16), dpToPx(16))
            setBackgroundColor(Color.parseColor(COLOR_BACKGROUND))
            gravity = Gravity.CENTER
        }
        addView(selectedNumbersTextView)
        
        // 创建数字按钮容器
        numbersContainer = LinearLayout(context).apply {
            orientation = VERTICAL
            setPadding(dpToPx(8), dpToPx(16), dpToPx(8), dpToPx(16))
        }
        addView(numbersContainer)
        
        createNumberButtons()
        updateSelectedNumbersDisplay()
    }
    
    private fun createNumberButtons() {
        // 创建5行，每行7个按钮 (1-35)
        for (row in 0 until 5) {
            val rowLayout = LinearLayout(context).apply {
                orientation = HORIZONTAL
                gravity = Gravity.CENTER
                layoutParams = LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(0, dpToPx(2), 0, dpToPx(2))
                }
            }
            
            for (col in 0 until 7) {
                val number = row * 7 + col + 1
                if (number <= 35) {
                    val button = createNumberButton(number)
                    numberButtons[number] = button
                    rowLayout.addView(button)
                } else {
                    // 添加空白占位符保持对齐
                    val spacer = TextView(context).apply {
                        layoutParams = LinearLayout.LayoutParams(dpToPx(44), dpToPx(44)).apply {
                            setMargins(dpToPx(2), dpToPx(2), dpToPx(2), dpToPx(2))
                        }
                    }
                    rowLayout.addView(spacer)
                }
            }
            
            numbersContainer.addView(rowLayout)
        }
    }
    
    private fun createNumberButton(number: Int): Button {
        return Button(context).apply {
            text = number.toString()
            
            // 设置固定尺寸
            val buttonSize = dpToPx(44)
            layoutParams = LinearLayout.LayoutParams(buttonSize, buttonSize).apply {
                setMargins(dpToPx(2), dpToPx(2), dpToPx(2), dpToPx(2))
            }
            
            // 设置文字样式
            textSize = 12f
            setTextColor(Color.BLACK)
            
            // 设置背景色 - 使用最基础的方法
            setBackgroundColor(Color.parseColor(COLOR_LIGHT_GRAY))
            
            // 设置点击监听器
            setOnClickListener {
                toggleNumber(number)
            }
        }
    }
    
    private fun toggleNumber(number: Int) {
        if (selectedNumbers.contains(number)) {
            // 取消选择
            selectedNumbers.remove(number)
            updateButtonAppearance(number, false)
        } else {
            // 选择数字
            selectedNumbers.add(number)
            updateButtonAppearance(number, true)
        }
        
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    private fun updateButtonAppearance(number: Int, isSelected: Boolean) {
        val button = numberButtons[number] ?: return
        
        if (isSelected) {
            // 选中状态：紫色背景，白色文字
            button.setBackgroundColor(Color.parseColor(COLOR_PURPLE))
            button.setTextColor(Color.WHITE)
        } else {
            // 未选中状态：浅灰色背景，黑色文字
            button.setBackgroundColor(Color.parseColor(COLOR_LIGHT_GRAY))
            button.setTextColor(Color.BLACK)
        }
    }
    
    private fun updateSelectedNumbersDisplay() {
        if (selectedNumbers.isEmpty()) {
            selectedNumbersTextView.text = "未选择任何数字"
        } else {
            val sortedNumbers = selectedNumbers.sorted().joinToString(", ")
            selectedNumbersTextView.text = "已选择的数字：$sortedNumbers"
        }
    }
    
    /**
     * 设置标题文本
     */
    fun setTitle(title: String) {
        titleTextView.text = title
    }
    
    /**
     * 获取当前选择的数字
     */
    fun getSelectedNumbers(): Set<Int> {
        return selectedNumbers.toSet()
    }
    
    /**
     * 设置选择的数字
     */
    fun setSelectedNumbers(numbers: Set<Int>) {
        clearSelection()
        numbers.forEach { number ->
            if (number in 1..35) {
                selectedNumbers.add(number)
                updateButtonAppearance(number, true)
            }
        }
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    /**
     * 清除所有选择
     */
    fun clearSelection() {
        selectedNumbers.forEach { number ->
            updateButtonAppearance(number, false)
        }
        selectedNumbers.clear()
        updateSelectedNumbersDisplay()
        onSelectionChanged?.invoke(selectedNumbers.toSet())
    }
    
    /**
     * 将dp转换为px
     */
    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            context.resources.displayMetrics
        ).toInt()
    }
}
