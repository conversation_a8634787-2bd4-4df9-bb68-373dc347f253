package com.lotteryfilter.app.model

/**
 * Represents a lottery combination of 5 numbers
 */
data class LotteryCombination(
    val numbers: List<Int>
) {
    init {
        require(numbers.size == 5) { "Combination must contain exactly 5 numbers" }
        require(numbers.all { it in 1..35 }) { "All numbers must be between 1 and 35" }
        require(numbers.distinct().size == 5) { "All numbers must be unique" }
    }
    
    /**
     * Returns the combination as a sorted list for consistent display
     */
    val sortedNumbers: List<Int> = numbers.sorted()
    
    /**
     * Checks if this combination contains any of the specified numbers
     */
    fun containsAny(numbersToCheck: Set<Int>): Boolean {
        return numbers.any { it in numbersToCheck }
    }
    
    /**
     * Checks if this combination can be formed from the given set of numbers
     */
    fun canBeFormedFrom(availableNumbers: Set<Int>): Boolean {
        return numbers.all { it in availableNumbers }
    }
    
    /**
     * Returns a formatted string representation of the combination
     */
    override fun toString(): String {
        return sortedNumbers.joinToString(", ")
    }
}
