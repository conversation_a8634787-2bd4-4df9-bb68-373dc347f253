package com.lotteryfilter.app.model

/**
 * Represents the filtering criteria for lottery combinations
 */
data class FilterCriteria(
    val excludedCombinationNumbers: Set<Int> = emptySet(),
    val excludedIndividualNumbers: Set<Int> = emptySet()
) {
    
    /**
     * Validates that all numbers are within the valid range (1-35)
     */
    fun isValid(): Boolean {
        val allNumbers = excludedCombinationNumbers + excludedIndividualNumbers
        return allNumbers.all { it in 1..35 }
    }
    
    /**
     * Checks if a combination should be excluded based on the criteria
     */
    fun shouldExcludeCombination(combination: LotteryCombination): Boolean {
        // Exclude if combination contains any individually excluded numbers
        if (combination.containsAny(excludedIndividualNumbers)) {
            return true
        }
        
        // Exclude if combination can be formed from excluded combination numbers
        if (excludedCombinationNumbers.isNotEmpty() && 
            combination.canBeFormedFrom(excludedCombinationNumbers)) {
            return true
        }
        
        return false
    }
    
    /**
     * Returns a summary of the filter criteria
     */
    fun getSummary(): String {
        val parts = mutableListOf<String>()
        
        if (excludedCombinationNumbers.isNotEmpty()) {
            parts.add("Excluded combination numbers: ${excludedCombinationNumbers.sorted().joinToString(", ")}")
        }
        
        if (excludedIndividualNumbers.isNotEmpty()) {
            parts.add("Excluded individual numbers: ${excludedIndividualNumbers.sorted().joinToString(", ")}")
        }
        
        return if (parts.isEmpty()) "No filters applied" else parts.joinToString("\n")
    }
}
