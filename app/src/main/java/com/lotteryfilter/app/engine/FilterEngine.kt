package com.lotteryfilter.app.engine

import com.lotteryfilter.app.model.FilterCriteria
import com.lotteryfilter.app.model.LotteryCombination
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.yield

/**
 * High-performance filtering engine for lottery combinations
 */
class FilterEngine {
    
    private val generator = CombinationGenerator()
    
    /**
     * Filters combinations based on criteria and returns results as a Flow
     */
    fun filterCombinations(criteria: FilterCriteria): Flow<FilterResult> = flow {
        var totalProcessed = 0
        var totalFiltered = 0
        val filteredCombinations = mutableListOf<LotteryCombination>()
        
        // Emit initial progress
        emit(FilterResult.Progress(0, 0, CombinationGenerator.TOTAL_COMBINATIONS))
        
        generator.generateCombinationsBatch(1000).collect { batch ->
            val validCombinations = batch.filterNot { combination ->
                criteria.shouldExcludeCombination(combination)
            }
            
            filteredCombinations.addAll(validCombinations)
            totalProcessed += batch.size
            totalFiltered += validCombinations.size
            
            // Emit progress update every batch
            emit(FilterResult.Progress(totalProcessed, totalFiltered, CombinationGenerator.TOTAL_COMBINATIONS))
            
            yield() // Allow cancellation
        }
        
        // Emit final results
        emit(FilterResult.Complete(filteredCombinations, totalProcessed, totalFiltered))
    }
    
    /**
     * Quick count of how many combinations would pass the filter without generating all results
     */
    fun countFilteredCombinations(criteria: FilterCriteria): Flow<CountResult> = flow {
        var totalProcessed = 0
        var totalFiltered = 0
        
        generator.generateCombinationsBatch(5000).collect { batch ->
            val validCount = batch.count { combination ->
                !criteria.shouldExcludeCombination(combination)
            }
            
            totalProcessed += batch.size
            totalFiltered += validCount
            
            emit(CountResult.Progress(totalProcessed, totalFiltered, CombinationGenerator.TOTAL_COMBINATIONS))
            
            yield() // Allow cancellation
        }
        
        emit(CountResult.Complete(totalProcessed, totalFiltered))
    }
    
    /**
     * Validates input and parses numbers from comma-separated string
     */
    fun parseNumbers(input: String): Set<Int> {
        if (input.isBlank()) return emptySet()
        
        return try {
            input.split(",")
                .map { it.trim() }
                .filter { it.isNotEmpty() }
                .map { it.toInt() }
                .filter { it in 1..35 }
                .toSet()
        } catch (e: NumberFormatException) {
            emptySet()
        }
    }
    
    /**
     * Creates FilterCriteria from user input strings
     */
    fun createFilterCriteria(
        excludeCombinationInput: String,
        excludeIndividualInput: String
    ): FilterCriteria {
        val excludedCombinationNumbers = parseNumbers(excludeCombinationInput)
        val excludedIndividualNumbers = parseNumbers(excludeIndividualInput)
        
        return FilterCriteria(
            excludedCombinationNumbers = excludedCombinationNumbers,
            excludedIndividualNumbers = excludedIndividualNumbers
        )
    }
}

/**
 * Sealed class representing filter operation results
 */
sealed class FilterResult {
    data class Progress(
        val processed: Int,
        val filtered: Int,
        val total: Int
    ) : FilterResult()
    
    data class Complete(
        val combinations: List<LotteryCombination>,
        val totalProcessed: Int,
        val totalFiltered: Int
    ) : FilterResult()
}

/**
 * Sealed class representing count operation results
 */
sealed class CountResult {
    data class Progress(
        val processed: Int,
        val filtered: Int,
        val total: Int
    ) : CountResult()
    
    data class Complete(
        val totalProcessed: Int,
        val totalFiltered: Int
    ) : CountResult()
}
