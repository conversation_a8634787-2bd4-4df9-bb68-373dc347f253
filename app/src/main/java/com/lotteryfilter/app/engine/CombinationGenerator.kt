package com.lotteryfilter.app.engine

import com.lotteryfilter.app.model.LotteryCombination
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.yield

/**
 * Efficient generator for lottery combinations using iterative approach
 * Generates combinations of 5 numbers from 1-35 (324,632 total combinations)
 */
class CombinationGenerator {
    
    companion object {
        const val MIN_NUMBER = 1
        const val MAX_NUMBER = 35
        const val COMBINATION_SIZE = 5
        const val TOTAL_COMBINATIONS = 324632 // C(35,5)
    }
    
    /**
     * Generates all possible combinations as a Flow for memory efficiency
     * Uses iterative algorithm to avoid recursion stack overflow
     */
    fun generateAllCombinations(): Flow<LotteryCombination> = flow {
        val combination = IntArray(COMBINATION_SIZE)
        
        // Initialize first combination [1, 2, 3, 4, 5]
        for (i in 0 until COMBINATION_SIZE) {
            combination[i] = MIN_NUMBER + i
        }
        
        while (true) {
            // Emit current combination
            emit(LotteryCombination(combination.toList()))
            yield() // Allow coroutine to be cancelled
            
            // Find the rightmost element that can be incremented
            var i = COMBINATION_SIZE - 1
            while (i >= 0 && combination[i] == MAX_NUMBER - COMBINATION_SIZE + 1 + i) {
                i--
            }
            
            // If no such element exists, we're done
            if (i < 0) break
            
            // Increment the found element
            combination[i]++
            
            // Set all elements to the right to consecutive values
            for (j in i + 1 until COMBINATION_SIZE) {
                combination[j] = combination[j - 1] + 1
            }
        }
    }
    
    /**
     * Generates combinations in batches for better performance
     */
    fun generateCombinationsBatch(batchSize: Int = 1000): Flow<List<LotteryCombination>> = flow {
        val batch = mutableListOf<LotteryCombination>()
        
        generateAllCombinations().collect { combination ->
            batch.add(combination)
            
            if (batch.size >= batchSize) {
                emit(batch.toList())
                batch.clear()
                yield() // Allow coroutine to be cancelled
            }
        }
        
        // Emit remaining combinations
        if (batch.isNotEmpty()) {
            emit(batch.toList())
        }
    }
    
    /**
     * Calculates the total number of combinations C(n,r) = n! / (r! * (n-r)!)
     */
    fun calculateTotalCombinations(n: Int = MAX_NUMBER, r: Int = COMBINATION_SIZE): Long {
        if (r > n) return 0
        if (r == 0 || r == n) return 1
        
        var result = 1L
        val k = minOf(r, n - r) // Take advantage of symmetry
        
        for (i in 0 until k) {
            result = result * (n - i) / (i + 1)
        }
        
        return result
    }
}
